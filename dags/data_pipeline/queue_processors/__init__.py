# coding=utf-8
"""
Queue Processors Package

This package provides a refactored architecture for queue processing operations
following SOLID and DRY principles. It includes:

- Abstract base classes for queue processors
- Configurable batch processing strategies  
- DataFrame processing pipelines
- Enhanced event coordination
- Concrete implementations for upsert operations

Key Components:
- AbstractQueueProcessor: Base class for all queue processors
- BatchProcessingStrategy: Configurable strategies for batch processing
- DataFrameProcessingPipeline: Reusable DataFrame processing components
- EnhancedEventCoordinator: Improved event coordination for referential integrity
- UpsertQueueProcessor: Refactored process_upsert_queue implementation
- UpsertOthersProcessor: Refactored process_upsert_others implementation

Usage:
    from dags.data_pipeline.queue_processors import (
        create_upsert_queue_processor,
        create_upsert_others_processor,
        process_upsert_queue_refactored,
        process_upsert_others_refactored
    )
"""

from .base_queue_processor import (
    AbstractQueueProcessor,
    BatchProcessingConfig,
    QueueProcessingResult,
    DataFramePreprocessor
)

from .batch_processing_strategy import (
    BatchProcessingStrategy,
    MessageCountStrategy,
    RecordCountStrategy,
    EventBasedStrategy,
    CompositeStrategy,
    AdaptiveStrategy,
    create_upsert_queue_strategy,
    create_upsert_others_strategy
)

from .dataframe_pipeline import (
    DataFrameProcessingPipeline,
    PreprocessingProcessor,
    ValidationProcessor,
    ConsolidationProcessor,
    ProcessingResult,
    ProcessingStage,
    create_standard_pipeline,
    create_consolidation_pipeline
)

from .event_coordination import (
    EnhancedEventCoordinator,
    ReferentialIntegrityCoordinator,
    ProcessingPhase,
    CoordinationState,
    enhanced_event_coordinator,
    referential_integrity_coordinator
)

from .concrete_processors import (
    UpsertQueueProcessor,
    UpsertOthersProcessor,
    create_upsert_queue_processor,
    create_upsert_others_processor,
    process_upsert_queue_refactored,
    process_upsert_others_refactored
)

# New simplified Protocol-based processors
from .protocols import (
    QueueProcessorProtocol,
    DataFrameProcessorProtocol,
    ReferentialIntegrityProtocol,
    CoordinationProtocol,
    ProcessingResult,
    ReferentialIntegrityManager,
    SimpleCoordinator,
    referential_integrity_manager,
    simple_coordinator
)

from .dataframe_processor import (
    DataFrameProcessor,
    dataframe_processor
)

from .simple_processors import (
    IssueQueueProcessor,
    OthersQueueProcessor,
    create_issue_queue_processor,
    create_others_queue_processor,
    process_upsert_queue_simple,
    process_upsert_others_simple
)

# Specialized consumer functions (migrated from queue_processors_new.py)
from .specialized_consumers import filter_null_ids, BaseQueueProcessor, ChangelogProcessor, WorklogProcessor, CommentProcessor, \
    IssueLinksProcessor, IssueProcessor, create_changelog_processor, create_worklog_processor, create_comment_processor, \
    create_issue_links_processor, create_issue_processor, consume_changelog, consume_worklog, consume_comment, \
    consume_issue_links, consume_issue

__all__ = [
    # Base classes
    "AbstractQueueProcessor",
    "BatchProcessingConfig", 
    "QueueProcessingResult",
    "DataFramePreprocessor",
    
    # Batch processing strategies
    "BatchProcessingStrategy",
    "MessageCountStrategy",
    "RecordCountStrategy", 
    "EventBasedStrategy",
    "CompositeStrategy",
    "AdaptiveStrategy",
    "create_upsert_queue_strategy",
    "create_upsert_others_strategy",
    
    # DataFrame processing
    "DataFrameProcessingPipeline",
    "PreprocessingProcessor",
    "ValidationProcessor",
    "ConsolidationProcessor",
    "ProcessingResult",
    "ProcessingStage",
    "create_standard_pipeline",
    "create_consolidation_pipeline",
    
    # Event coordination
    "EnhancedEventCoordinator",
    "ReferentialIntegrityCoordinator",
    "ProcessingPhase",
    "CoordinationState",
    "enhanced_event_coordinator",
    "referential_integrity_coordinator",
    
    # Concrete processors (legacy ABC-based)
    "UpsertQueueProcessor",
    "UpsertOthersProcessor",
    "create_upsert_queue_processor",
    "create_upsert_others_processor",
    "process_upsert_queue_refactored",
    "process_upsert_others_refactored",

    # New Protocol-based interfaces
    "QueueProcessorProtocol",
    "DataFrameProcessorProtocol",
    "ReferentialIntegrityProtocol",
    "CoordinationProtocol",
    "ProcessingResult",
    "ReferentialIntegrityManager",
    "SimpleCoordinator",
    "referential_integrity_manager",
    "simple_coordinator",

    # New simplified processors
    "DataFrameProcessor",
    "dataframe_processor",
    "IssueQueueProcessor",
    "OthersQueueProcessor",
    "create_issue_queue_processor",
    "create_others_queue_processor",
    "process_upsert_queue_simple",
    "process_upsert_others_simple",

    # Specialized consumer functions
    "BaseQueueProcessor",
    "filter_null_ids",
    "ChangelogProcessor",
    "WorklogProcessor",
    "CommentProcessor",
    "IssueLinksProcessor",
    "IssueProcessor",
    "create_changelog_processor",
    "create_worklog_processor",
    "create_comment_processor",
    "create_issue_links_processor",
    "create_issue_processor",
    "consume_changelog",
    "consume_worklog",
    "consume_comment",
    "consume_issue_links",
    "consume_issue"
]
