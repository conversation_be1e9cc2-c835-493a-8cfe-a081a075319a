# coding=utf-8
"""
Specialized consumer functions for JIRA data processing.

This module contains the specialized consumer functions that were previously in
queue_processors_new.py. These functions handle the transformation of specific
JIRA data types (changelog, worklog, comments, issue links, issues).

These are different from the queue processors - they are the actual data
transformation functions that process JIRA API responses into database-ready DataFrames.
"""
import asyncio
import os
import sys
import traceback
from asyncio import Queue
from datetime import datetime
from logging import Logger
from typing import Optional, Any, Dict

import aiohttp
import markdownify
import pandas as pd

from dags.data_pipeline.data_type_handlers import create_robust_type_handler
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_dataframe, quick_save_async
from dags.data_pipeline.dbmodels.initiativeattribute import InitiativeAttribute
from dags.data_pipeline.debug.debug_utils import debug_queue_operation
from dags.data_pipeline.field_mappers import get_field_mapper
from dags.data_pipeline.jira.api_client import fetch_with_retries_get
from dags.data_pipeline.priority_queue_system import priority_queue_manager, MessageType

from dags.data_pipeline.specialized_field_mappers import create_specialized_processor


# Re-export everything for backward compatibility
def filter_null_ids(
    df: pd.DataFrame,
    calling_function: str,
    id_column: str = "id",
    issue_key_column: str = "issue_key",
    my_logger: Optional[Logger] = None
) -> pd.DataFrame:
    # Use logger.debug if valid logger is passed, else fallback to print
    logger = my_logger.debug if isinstance(my_logger, Logger) else print

    if id_column not in df.columns:
        logger(f"[{calling_function}] Column '{id_column}' does not exist.")
        return df

    null_id_rows = df[df[id_column].isnull()]

    if not null_id_rows.empty:
        if issue_key_column in null_id_rows.columns:
            issue_keys = null_id_rows[issue_key_column].dropna().tolist()
            logger(f"[{calling_function}] Filtered out {len(issue_keys)} row(s) with null '{id_column}': issue_keys = {issue_keys}")
        else:
            logger(f"[{calling_function}] Filtered out rows with null '{id_column}', but '{issue_key_column}' column not found.")

    return df[df[id_column].notnull()]


class BaseQueueProcessor:
    """Base class for queue processors with common functionality."""

    def __init__(self):
        self.field_mapper = get_field_mapper()
        self.type_handler = create_robust_type_handler()
        # self.base_url = "https://corecard.atlassian.net"
        self.base_url = os.environ.get("JIRA_BASE_URL", "https://corecard.atlassian.net")

    async def process_queue_item(
        self,
        item: Any,
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """
        Process a single queue item. To be implemented by subclasses.

        Args:
            item: Queue item to process
            output_queue: Queue for upsert operations (queue_upsert_issue or queue_upsert_others)
            http_session: HTTP session for JIRA API calls
            my_logger: Logger instance

        Returns:
            True if processing should continue, False to stop
        """
        raise NotImplementedError("Subclasses must implement process_queue_item")

    async def run_processor(
        self,
        queue_id: int,
        name: str,
        input_queue: Queue,
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ):
        """
        Main processor loop with error handling.

        Args:
            queue_id: Processor ID
            name: Processor name
            input_queue: Input queue to process
            output_queue: Output queue for upsert operations (queue_upsert_issue or queue_upsert_others)
            http_session: HTTP session for JIRA API calls
            my_logger: Logger instance
        """
        try:
            loop_count = 0

            while True:
                loop_count += 1
                item = None
                item_fetched = False

                # Check for termination before processing
                try:
                    from dags.data_pipeline.scalable_coordination import coordination_manager
                    queue_name = f"queue_{name}"
                    if await coordination_manager.should_terminate_consumer(queue_name):
                        if my_logger:
                            my_logger.info(f"Consumer {name} terminating - all producers done and queue empty")
                        break
                except Exception as coord_error:
                    if my_logger:
                        my_logger.debug(f"Coordination check failed: {coord_error}")

                try:
                    # Use timeout to avoid indefinite blocking
                    try:
                        # async with debug_queue_operation(input_queue, "get", f"queue_{name}") as item:
                        item = await asyncio.wait_for(
                            priority_queue_manager.get_priority_message(input_queue),
                            timeout=5.0  # 1 second timeout
                        )
                        item_fetched = True
                        my_logger.debug(f"Queue size queue_{name}: {input_queue.qsize()}, {output_queue.qsize()}")

                        should_continue = await self.process_queue_item(
                            item, output_queue, http_session, my_logger
                        )

                        if not should_continue:
                            await priority_queue_manager.put_priority_message(
                                output_queue, None, MessageType.TERMINATION, queue_id
                            )
                            break

                    except asyncio.TimeoutError:
                        # Timeout occurred, check if we should continue
                        if my_logger:
                            my_logger.debug(f"Queue get timeout in {name}, checking termination")
                        continue

                except asyncio.CancelledError:
                    my_logger.info(f"Task {name} received cancellation request, stopping processing loop.")
                    break

                finally:
                    # Only call task_done if an item was successfully fetched
                    if item_fetched:
                        try:
                            async with debug_queue_operation(input_queue, "task_done", f"queue_{name}"):
                                pass
                        except ValueError as ve:
                            if my_logger:
                                my_logger.warning(f"task_done() error in queue_{name}: {ve}")

        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            my_logger.exception(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
            raise e
        finally:
            if my_logger:
                my_logger.debug(f"Queue size queue_{name}: {input_queue.qsize()}")
                my_logger.info(f"{name} processor is done!")


class ChangelogProcessor(BaseQueueProcessor):
    """Processor for changelog data with additional JIRA data fetching."""

    def __init__(self):
        super().__init__()
        self.specialized_processor = create_specialized_processor('changelog')

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process changelog DataFrame with additional data fetching."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty changelog DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)

            # Apply specialized DataFrame transformations
            df = self.specialized_processor.process_dataframe(df, my_logger)

            # Fetch additional changelog data from JIRA if needed
            df = await self._fetch_additional_changelog_data(df, http_session, my_logger)
            df = filter_null_ids(df, "changelog", "id", "key", my_logger)

            # Apply type conversions using specialized field mapper
            type_mappings = self.specialized_processor.field_mapper.get_type_mappings()
            df, conversion_results = self.type_handler.safe_astype_conversion(df, type_mappings, my_logger)

            # Get database configuration
            db_config = self.specialized_processor.field_mapper.get_database_config()

            # Queue changelog data with SPECIALIZED priority
            await priority_queue_manager.put_priority_message(
                output_queue,
                {
                    "df": df,
                    **db_config
                },
                MessageType.SPECIALIZED,
                2  # Default task_id for specialized processors
            )

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing changelog data: {e}", exc_info=True)
            raise

    async def _fetch_additional_changelog_data(
        self,
        df: pd.DataFrame,
        http_session: aiohttp.ClientSession,
        my_logger
    ) -> pd.DataFrame:
        """Fetch additional changelog data from JIRA based on conditions."""

        if 'issue_key' not in df.columns or 'total' not in df.columns:
            if my_logger:
                my_logger.debug("No issue_key or total column found, skipping additional data fetch")
            return df

        # Create tasks for fetching additional changelog data
        # Similar to the original logic in consume_changelog_old
        changelog_records = df[['issue_id', 'issue_key', 'total']].to_dict('records')

        async def fetch_changelog_data(issue_id, issue_key, start_at):
            """Inner function to fetch changelog data for a specific range."""
            """
                    Inner function to fetch changelog data for a specific range.
                    Enhanced with proper error handling from original code.
                    """
            asyncio.current_task().set_name("fetch_changelog_data")
            base_result = {
                "issue_id": issue_id,
                "issue_key": issue_key,
                "start_at": start_at,
                "success": False,
                "error": None
            }
            try:
                changelog = await fetch_changelog(
                    http_session, self.base_url, issue_key, params={'startAt': start_at},  my_logger=my_logger
                )
                if not changelog.get('success', False):
                    error_msg = changelog.get('exception', 'Unknown error from fetch_changelog')
                    if my_logger:
                        my_logger.error(f"Failed to fetch changelog for {issue_key} (start_at={start_at}): {error_msg}")

                    base_result.update({
                        "error": error_msg,
                        "startAt": start_at,
                        "maxResults": 0,
                        "total": 0,
                        "histories": []
                    })
                    return base_result

                # Success case - extract the data
                result_data = changelog.get('result', {})
                base_result.update({
                    "success": True,
                    "startAt": result_data.get('startAt', start_at),
                    "maxResults": result_data.get('maxResults', 0),
                    "total": result_data.get('total', 0),
                    "histories": result_data.get('values', [])
                })
                return base_result

            except aiohttp.ClientResponseError as err:
                error_msg = f"HTTP error {err.status}: {err.message}"
                my_logger.error(f"ClientResponseError for {issue_key} (start_at={start_at}): {error_msg}")
                base_result.update({
                    "error": error_msg,
                    "startAt": start_at,
                    "maxResults": 0,
                    "total": 0,
                    "histories": []
                })
                return base_result

            except asyncio.TimeoutError as err:
                error_msg = f"Request timeout: {str(err)}"
                my_logger.error(f"Timeout error for {issue_key} (start_at={start_at}): {error_msg}")
                base_result.update({
                    "error": error_msg,
                    "startAt": start_at,
                    "maxResults": 0,
                    "total": 0,
                    "histories": []
                })
                return base_result

            except Exception as err:
                error_msg = f"Unexpected error: {str(err)}"
                if my_logger:
                    my_logger.error(f"Unexpected error for {issue_key} (start_at={start_at}): {error_msg}")
                base_result.update({
                    "error": error_msg,
                    "startAt": start_at,
                    "maxResults": 0,
                    "total": 0,
                    "histories": []
                })
                return base_result
            finally:
                my_logger.info(f"Processed changelog fetch for {issue_key} (start_at={start_at})")

        # Create tasks for fetching changelog data
        # changelog endpoint returns changelogs in ascending order
        # issue search returns changelogs in descending order.
        # Therefore, start range needs to be 0 and end range needs to be total records - 100
        tasks_changelog = [
            fetch_changelog_data(record['issue_id'], record['issue_key'], start_at)
            for record in changelog_records
            for start_at in range(0, max(0, record['total'] - 100), 100)
        ]

        if tasks_changelog:
            if my_logger:
                my_logger.info(f"Fetching additional changelog data for {len(tasks_changelog)} requests")

            # Execute all tasks concurrently
            changelog_results = await asyncio.gather(*tasks_changelog, return_exceptions=True)

            # Process results and merge with original DataFrame
            # MISSING LOGIC: Enhanced result processing with proper validation
            valid_results = []
            for result in changelog_results:
                if (isinstance(result, dict) and
                        'issue_id' in result and
                        'issue_key' in result and
                        'histories' in result):
                    valid_results.append(result)
                elif isinstance(result, Exception):
                    if my_logger:
                        my_logger.error(f"Exception in changelog fetch task: {result}")
                else:
                    if my_logger:
                        my_logger.warning(f"Invalid result: {result}")

            if valid_results:
                additional_df = pd.DataFrame(valid_results)
                # Merge or append additional data to the original DataFrame
                df = pd.concat([df, additional_df], ignore_index=True)
                if my_logger:
                    my_logger.info(f"Added {len(valid_results)} additional changelog records")

        return df


class WorklogProcessor(BaseQueueProcessor):
    """Processor for worklog data with additional JIRA data fetching."""

    def __init__(self):
        super().__init__()
        self.specialized_processor = create_specialized_processor('worklog')

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process worklog DataFrame with additional data fetching."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty worklog DataFrame")
            return False

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)
            # Apply specialized DataFrame transformations
            df, worklog_keys = self.specialized_processor.process_dataframe(df, my_logger)

            # Fetch additional worklog data from JIRA if needed
            my_logger.info(f"Worklog keys: {worklog_keys}")
            if len(worklog_keys) > 0:
                df_additional_worklog = await self._fetch_additional_worklog_data(worklog_keys, http_session, my_logger)
                if df_additional_worklog.shape[0] > 0:
                    df = pd.concat([df, df_additional_worklog], ignore_index=True)


            df = self.specialized_processor.field_mapper.drop_unwanted_columns(df, my_logger)
            df = self.specialized_processor.field_mapper.apply_field_mappings(df, my_logger)

            df = filter_null_ids(df, "worklog", "id", "issue_key", my_logger)
            # Apply type conversions using specialized field mapper
            type_mappings = self.specialized_processor.field_mapper.get_type_mappings()
            df, conversion_results = self.type_handler.safe_astype_conversion(df, type_mappings, my_logger)
            df.drop_duplicates(subset='id', inplace=True, keep='first')
            # if "comment" in df.columns:
            #     df.drop(columns=["comment"], inplace=True)

            db_config = self.specialized_processor.field_mapper.get_database_config()
            # Queue worklog data with SPECIALIZED priority
            await priority_queue_manager.put_priority_message(
                output_queue,
                {
                    "df": df,
                    **db_config
                },
                MessageType.SPECIALIZED,
                3  # Default task_id for specialized processors
            )

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing worklog data: {e}", exc_info=True)
            raise

    async def _fetch_additional_worklog_data(
        self,
        worklog_keys: list,
        http_session: aiohttp.ClientSession,
        my_logger
    ) -> pd.DataFrame:
        """Fetch additional worklog data from JIRA based on conditions."""

        async def fetch_worklog_data(issue_key, start_at: int=0):
            """Inner function to fetch worklog data for a specific issue."""
            asyncio.current_task().set_name(f"fetch_worklog_data_{issue_key}")
            try:
                worklog_response = await fetch_worklog(http_session, self.base_url, issue_key, my_logger)
                if not worklog_response.get('success', False):
                    error_msg = worklog_response.get('exception', 'Unknown error from fetch_worklog')
                    if my_logger:
                        my_logger.error(f"Failed to fetch worklog for {issue_key}: {error_msg}")
                    return []

                # Success case - extract the worklog data
                worklogs = worklog_response.get('result', [])
                for worklog in worklogs:
                    worklog['issue_key'] = issue_key

                return worklogs

            except Exception as err:
                if my_logger:
                    my_logger.error(f"Unexpected error fetching worklog for {issue_key}: {str(err)}")
                return []

        # Create tasks for fetching worklog data
        tasks_worklog = [fetch_worklog_data(issue_key) for issue_key in worklog_keys]

        if tasks_worklog:
            if my_logger:
                my_logger.info(f"Fetching additional worklog data for {len(tasks_worklog)} issues")

            # Execute all tasks concurrently
            worklog_results = await asyncio.gather(*tasks_worklog, return_exceptions=True)

            # Process results and merge with original DataFrame
            additional_data = []
            for result in worklog_results:
                if isinstance(result, Exception):
                    if my_logger:
                        my_logger.error(f"Exception in worklog fetch task: {result}")
                    continue

                if result and isinstance(result, list):  # result is a list of worklogs
                    additional_data.extend(result)

            if additional_data:
                df = pd.DataFrame(additional_data)
                df['author'] = df['author'].apply(lambda x: x['accountId'] if isinstance(x, dict) else x)
                df['updateAuthor'] = df['updateAuthor'].apply(lambda x: x['accountId'] if isinstance(x, dict) else x)
                df.rename(columns={'author': 'author.accountId', 'updateAuthor': 'updateAuthor.accountId'}, inplace=True)

                return df

        return pd.DataFrame(
            columns=[
                'self', 'author', 'updateAuthor',
                'comment',
                'created', 'updated', 'started', 'timeSpent', 'timeSpentSeconds',
                'id', 'issueId', 'issue_key'
            ]
        )


class CommentProcessor(BaseQueueProcessor):
    """Processor for comment data with additional JIRA data fetching."""

    def __init__(self):
        super().__init__()
        self.specialized_processor = create_specialized_processor('comment')

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process comment DataFrame with additional data fetching."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty comment DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)

            # Apply specialized DataFrame transformations
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # Step 1: Normalize comment data

            df = df.join(pd.json_normalize(df.comment)).drop(columns=["comment", "self"])
            df.query("total > 0", inplace=True)
            df_additional = df.query("total > maxResults")

            df = self.specialized_processor.process_dataframe(df, my_logger)
            df.rename(columns={'id': 'issue_id', 'key': 'issue_key'}, inplace=True)
            comments_df = pd.json_normalize(df['comments'])
            df = df.drop(columns=['comments']).join(comments_df).reset_index(drop=True)


            # Fetch additional comment data from JIRA if needed
            df_additional = await self._fetch_additional_comment_data(df_additional, http_session, my_logger)


            df = pd.concat([df, df_additional], ignore_index=True)

            df = self.specialized_processor.field_mapper.drop_unwanted_columns(df, my_logger)
            df = self.specialized_processor.field_mapper.apply_field_mappings(df, my_logger)
            df = filter_null_ids(df, "comment", "id", "issue_key", my_logger)
            # Apply type conversions using specialized field mapper
            type_mappings = self.specialized_processor.field_mapper.get_type_mappings()
            df, conversion_results = self.type_handler.safe_astype_conversion(df, type_mappings, my_logger)

            # Get database configuration
            db_config = self.specialized_processor.field_mapper.get_database_config()

            # Queue comment data with SPECIALIZED priority
            await priority_queue_manager.put_priority_message(
                output_queue,
                {
                    **db_config,
                    "df": df,
                },
                MessageType.SPECIALIZED,
                4  # Default task_id for specialized processors
            )

            return True

        except Exception as e:
            if my_logger:
                exc_type, exc_value, exc_tb = sys.exc_info()
                line_num = exc_tb.tb_lineno
                tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
                my_logger.exception(
                    f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                    exc_info=True
                )
                my_logger.error(f"Error processing comment data: {e}", exc_info=True)
            raise

    async def _fetch_additional_comment_data(
        self,
        df: pd.DataFrame,
        http_session: aiohttp.ClientSession,
        my_logger
    ) -> pd.DataFrame:
        """Fetch additional comment data from JIRA based on conditions."""
         # Get unique issue keys that need additional comment data
        if df.shape[0] == 0:
            my_logger.debug(f"No additional comment data to fetch. returning {df.columns}")
            return pd.DataFrame(columns=df.columns)
        issue_keys = df[['id', 'key']].values.tolist()
        my_logger.info(f"issue_keys: {issue_keys}")

        async def fetch_comment_data(issue_key):
            """Inner function to fetch comment data for a specific issue."""
            try:
                comment_response = await fetch_comments(http_session, self.base_url, issue_key[1], my_logger)
                if not comment_response.get('success', False):
                    if my_logger:
                        my_logger.error(f"Failed to fetch comments for {issue_key[1]}")
                    return []

                # Success case - extract the comment data
                comments = comment_response.get('comments', [])
                for comment in comments:
                    comment['issue_id'] = issue_key[0]
                    comment['issue_key'] = issue_key[1]

                return comments

            except Exception as err:
                if my_logger:
                    my_logger.error(f"Unexpected error fetching comments for {issue_key}: {str(err)}")
                return []

        # Create tasks for fetching comment data
        tasks_comment = [fetch_comment_data(issue_key) for issue_key in issue_keys]

        # Execute all tasks concurrently
        comment_results = await asyncio.gather(*tasks_comment, return_exceptions=True)

        # Process results and merge with original DataFrame
        additional_data = []
        for result in comment_results:
            if isinstance(result, Exception):
                if my_logger:
                    my_logger.error(f"Exception in comment fetch task: {result}")
                continue

            if result and isinstance(result, list):  # result is a list of comments
                additional_data.extend(result)

        if additional_data:
            df_additional = pd.DataFrame(additional_data)
            df_additional['author'] = df_additional['author'].apply(lambda x: x['accountId'] if isinstance(x, dict) else x)
            df_additional['updateAuthor'] = df_additional['updateAuthor'].apply(
                lambda x: x['accountId'] if isinstance(x, dict) else x)

            df_additional.rename(columns={'author': 'author.accountId', 'updateAuthor': 'updateAuthor.accountId'},
                               inplace=True)

            return df_additional
        else:
            return pd.DataFrame(columns=df.columns)


class IssueLinksProcessor(BaseQueueProcessor):
    """Processor for issue links data."""

    def __init__(self):
        super().__init__()
        self.specialized_processor = create_specialized_processor('issue_links')

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process issue links DataFrame."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty issue links DataFrame")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)
            # Drop rows with empty issuelinks
            df = df[df['issuelinks'].apply(lambda x: len(x) > 0)]
            df.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
            if df.shape[0] == 0:
                if my_logger:
                    my_logger.info(f"Received empty issue links DataFrame after cleaning")
                return True

            # Apply specialized DataFrame transformations
            df = self.specialized_processor.process_dataframe(df, my_logger)
            df = filter_null_ids(df, "issue_links", "id", "issue_key", my_logger)

            # Apply type conversions using specialized field mapper
            type_mappings = self.specialized_processor.field_mapper.get_type_mappings()
            df, conversion_results = self.type_handler.safe_astype_conversion(df, type_mappings, my_logger)

            # Get database configuration
            db_config = self.specialized_processor.field_mapper.get_database_config()

            # Queue issue links data with SPECIALIZED priority
            await priority_queue_manager.put_priority_message(
                output_queue,
                {
                    **db_config,
                    "df": df,
                },
                MessageType.SPECIALIZED,
                5  # Default task_id for specialized processors
            )

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing issue links data: {e}", exc_info=True)
            raise


class IssueProcessor(BaseQueueProcessor):
    """Processor for issue data with enhanced field mapping and type conversion."""

    def __init__(self):
        super().__init__()
        # Create specialized processor for issues to access database config
        from dags.data_pipeline.specialized_field_mappers import SpecializedFieldMapper
        # self.specialized_field_mapper = SpecializedFieldMapper('enhanced_issue')
        self.specialized_field_mapper = SpecializedFieldMapper('issue')

    async def process_queue_item(
        self,
        df: Optional[pd.DataFrame],
        output_queue: Queue,
        http_session: aiohttp.ClientSession,
        my_logger=None
    ) -> bool:
        """Process issue DataFrame with field mapping and type conversion."""
        if df is None:
            return False

        if df.shape[0] == 0:
            if my_logger:
                my_logger.info(f"Received empty DataFrame, queue size: {output_queue.qsize()}")
            return True

        try:
            # Remove completely empty columns
            df.dropna(how='all', axis=1, inplace=True)

            # Apply special data conversions first
            df = self.type_handler.handle_special_conversions(df, my_logger)

            # Handle description field reconstruction
            df = self._handle_description_field(df, my_logger)

            # Drop unwanted columns
            df = self._drop_unwanted_columns(df, my_logger)

            # Apply field mappings from YAML configuration
            df = self.field_mapper.apply_field_mappings(df, my_logger)

            # Handle initiative attributes
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            await quick_save_async(df, f"df_issue_after_field_mapping_{timestamp}", path=f"c:/vishal/log/issue")
            df_initiative_attribute = self._extract_initiative_attributes(df, my_logger)

            # Apply robust type conversion
            df, conversion_results = self.type_handler.apply_field_based_type_conversion(df, my_logger)

            # Handle remaining manual type conversions
            df = self._handle_manual_type_conversions(df, my_logger)

            # Process description markdown
            if 'description_markdown' in df.columns:
                df['description_markdown'] = df['description_markdown'].apply(
                    lambda x: markdownify.markdownify(x, heading_style='ATX') if pd.notna(x) else x
                )

            # Handle timetracking
            df = self._handle_timetracking(df, my_logger)

            # Handle datetime conversions
            # df = self._handle_datetime_conversions(df, my_logger)
            if 'isSubTask' in df.columns:
                df['isSubTask'] = df['isSubTask'].astype(bool)
            # df['id'] = df['id'].astype('Int64')


            # Get database configuration from YAML
            db_config = self.specialized_field_mapper.get_database_config()

            # Queue main issue data with ISSUE_DIRECT priority (highest priority)
            await priority_queue_manager.put_priority_message(
                output_queue,
                {
                    **db_config,
                    "df": df,
                },
                MessageType.ISSUE_DIRECT,
                0  # Default task_id for issue processor
            )


            # Queue initiative attributes if any
            if df_initiative_attribute.shape[0] > 0:
                await self._queue_initiative_attributes(df_initiative_attribute, output_queue, my_logger)

            # Log conversion summary
            summary = self.type_handler.get_conversion_summary()
            if summary["total_conversions"] > 0 and my_logger:
                my_logger.info(f"Type conversion summary: {summary['successful']}/{summary['total_conversions']} successful")

            return True

        except Exception as e:
            if my_logger:
                my_logger.error(f"Error processing issue data: {e}", exc_info=True)
            raise

    def _handle_description_field(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle description field reconstruction."""
        if 'description.type' in df.columns:
            condition = df['description.type'].notna()
            df.loc[condition, 'description'] = df.loc[condition].apply(
                lambda row: {
                    'type': row['description.type'],
                    "version": row['description.version'],
                    "content": row['description.content']
                },
                axis=1
            )
        return df

    def _drop_unwanted_columns(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Drop unwanted columns based on configuration."""
        drop_prefixes = self.field_mapper.get_drop_column_prefixes()
        exceptions = self.field_mapper.get_drop_column_exceptions()

        columns_to_drop = [
            col for col in df.columns
            if any(col.startswith(prefix) for prefix in drop_prefixes) and col not in exceptions
        ]

        if columns_to_drop:
            df.drop(columns=columns_to_drop, inplace=True)
            if my_logger:
                my_logger.debug(f"Dropped {len(columns_to_drop)} unwanted columns")

        return df

    def _extract_initiative_attributes(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Extract and process initiative attributes."""
        try:
            df_initiative_attribute = df[df['issuetype'] == "Initiative"].copy()
        except Exception as e:
            my_logger.error(f"Failed to extract initiative attributes: {e}")
            quick_save_dataframe(df, f"df_issue_after_field_mapping_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}", path=f"c:/vishal/log/issue")
            raise e

        if df_initiative_attribute.shape[0] > 0:
            required_columns = [
                'id', 'key', 'customfield_10182', 'customfield_10183', 'customfield_10184',
                'created', 'updated'
            ]

            # Add missing columns with NaN values
            for col in required_columns:
                if col not in df_initiative_attribute.columns:
                    df_initiative_attribute[col] = float('nan')

            df_initiative_attribute = df_initiative_attribute[required_columns]

            # Apply initiative attributes field mapping
            initiative_mapping = self.field_mapper.get_field_mapping('initiative_attributes')
            if initiative_mapping and initiative_mapping.mapping:
                df_initiative_attribute.rename(columns=initiative_mapping.mapping, inplace=True)

            # Apply type conversions using field mapping
            df_initiative_attribute, _ = self.type_handler.apply_field_based_type_conversion(
                df_initiative_attribute, my_logger
            )

            # Drop the original columns from main df
            columns_to_drop = ["customfield_10182", "customfield_10183", "customfield_10184"]
            existing_columns_to_drop = [col for col in columns_to_drop if col in df.columns]
            if existing_columns_to_drop:
                df.drop(columns=existing_columns_to_drop, inplace=True)

        return df_initiative_attribute

    def _handle_manual_type_conversions(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle manual type conversions that need special logic."""
        # Define columns that need special handling
        special_int_columns = ['id', 'parent_id', 'reopen_count', 'issue_hierarchy_level']

        for col in special_int_columns:
            if col in df.columns:
                try:
                    # if my_logger:
                    #     my_logger.debug(f"Processing special int column: {col}")
                    #     my_logger.debug(f"  Data type: {df[col].dtype}")
                    #     my_logger.debug(f"  Non-null count: {df[col].notna().sum()}/{len(df)}")

                    # Apply robust conversion
                    df, _ = self.type_handler.safe_astype_conversion(
                        df, {col: 'Int64'}, my_logger
                    )

                except Exception as e:
                    if my_logger:
                        my_logger.error(f"Failed to convert special column '{col}': {e}")

        return df

    def _handle_timetracking(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle timetracking field processing."""
        timetracking_columns = [
            'timetracking.timeSpent',
            'timetracking.remainingEstimate',
            'timetracking.originalEstimate',
            'timetracking.timeSpentSeconds',
            'timetracking.remainingEstimateSeconds',
            'timetracking.originalEstimateSeconds'
        ]

        # Add missing columns
        for col in timetracking_columns:
            if col not in df.columns:
                df[col] = None

        # Create timetracking JSON
        df['timetracking'] = df[timetracking_columns].apply(
            lambda row: {} if row.isna().all() else {
                timetracking.split('.')[1]: row[timetracking]
                for timetracking in timetracking_columns
                if pd.notna(row[timetracking]) and row[timetracking] is not None
            },
            axis=1
        )

        # Drop original columns
        df.drop(columns=timetracking_columns, inplace=True)

        return df

    def _handle_datetime_conversions(self, df: pd.DataFrame, my_logger) -> pd.DataFrame:
        """Handle datetime field conversions."""
        datetime_columns = ['statuscategorychangedate', 'resolutiondate', 'created', 'updated']
        existing_datetime_cols = [col for col in datetime_columns if col in df.columns]

        if existing_datetime_cols:
            df[existing_datetime_cols] = df[existing_datetime_cols].apply(pd.to_datetime, errors='coerce')

        # Handle date columns
        for col in ['startdate', 'duedate']:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors="coerce").dt.date

        return df

    async def _queue_initiative_attributes(
        self,
        df_initiative_attribute: pd.DataFrame,
        output_queue: Queue,
        my_logger
    ):
        """Queue initiative attributes for upsert."""
        # Convert datetime columns
        for col in ['created', 'updated']:
            if col in df_initiative_attribute.columns:
                df_initiative_attribute[col] = pd.to_datetime(df_initiative_attribute[col])

        df_initiative_attribute.rename(columns={"id": "initiative_id", "key": "initiative_key"}, inplace=True)
        df_initiative_attribute["initiative_id"] = df_initiative_attribute["initiative_id"].astype(pd.Int64Dtype())

        await priority_queue_manager.put_priority_message(
            output_queue,
            {
                "model": InitiativeAttribute,
                "df": df_initiative_attribute,
                "primary_key": "initiative_id",
                "no_update_cols": ("attr",),
                "on_conflict_update": True,
            },
            MessageType.ISSUE_DIRECT,
            1  # Default task_id for issue processor
        )


def create_changelog_processor() -> ChangelogProcessor:
    """Create a new ChangelogProcessor instance."""
    return ChangelogProcessor()


def create_worklog_processor() -> WorklogProcessor:
    """Create a new WorklogProcessor instance."""
    print(f"DEBUG: create_worklog_processor called, returning WorklogProcessor")
    return WorklogProcessor()


def create_comment_processor() -> CommentProcessor:
    """Create a new CommentProcessor instance."""
    print(f"DEBUG: create_comment_processor called, returning CommentProcessor")
    return CommentProcessor()


def create_issue_links_processor() -> IssueLinksProcessor:
    """Create a new IssueLinksProcessor instance."""
    print(f"DEBUG: create_issue_links_processor called, returning IssueLinksProcessor")
    return IssueLinksProcessor()


def create_issue_processor() -> IssueProcessor:
    """Create a new IssueProcessor instance."""
    print(f"DEBUG: create_issue_processor called, returning IssueProcessor")
    return IssueProcessor()


async def consume_changelog(
    queue_id: int,
    name: str,
    queue_changelog: Queue,
    output_queue: Queue,  # This will be queue_upsert_others for changelog
    project_key: str,
    # pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_changelog function using the new processor architecture."""
    # Set task name for monitoring
    # asyncio.current_task().set_name(f"changelog_processor_{queue_id}")

    if my_logger:
        my_logger.info(f"DEBUG: consume_changelog function called!")
    print(f"DEBUG: consume_changelog function called!")

    try:
        processor = create_changelog_processor()

        # Add debugging to verify correct processor type
        if my_logger:
            my_logger.info(f"Created processor type: {type(processor).__name__}")
            if not isinstance(processor, ChangelogProcessor):
                my_logger.error(f"ERROR: Expected ChangelogProcessor, got {type(processor).__name__}")
                raise TypeError(f"Expected ChangelogProcessor, got {type(processor).__name__}")

        await processor.run_processor(
            queue_id=queue_id,
            name="changelog",
            input_queue=queue_changelog,
            output_queue=output_queue,
            http_session=http_session,
            my_logger=my_logger
        )
    except Exception as e:
        # Re-raise the exception - task state will be tracked automatically
        raise


async def consume_worklog(
    queue_id: int,
        name: str,
    queue_worklog: Queue,
    output_queue: Queue,  # This will be queue_upsert_others for worklog
    project_key: str,
    # pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_worklog function using the new processor architecture."""
    # Set task name for monitoring
    # asyncio.current_task().set_name(f"worklog_processor_{queue_id}")

    if my_logger:
        my_logger.info(f"DEBUG: consume_worklog function called!")
    print(f"DEBUG: consume_worklog function called!")

    try:
        processor = create_worklog_processor()

        # Add debugging to verify correct processor type
        if my_logger:
            my_logger.info(f"Created processor type: {type(processor).__name__}")
            if not isinstance(processor, WorklogProcessor):
                my_logger.error(f"ERROR: Expected WorklogProcessor, got {type(processor).__name__}")
                raise TypeError(f"Expected WorklogProcessor, got {type(processor).__name__}")

        await processor.run_processor(
            queue_id=queue_id,
            name="worklog",
            input_queue=queue_worklog,
            output_queue=output_queue,
            http_session=http_session,
            my_logger=my_logger
        )
    except Exception as e:
        # Re-raise the exception - task state will be tracked automatically
        raise


async def consume_comment(
    queue_id: int,
        name: str,
    queue_comment: Queue,
    output_queue: Queue,  # This will be queue_upsert_others for comment
    project_key: str,
    # pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_comment function using the new processor architecture."""
    # Set task name for monitoring
    # asyncio.current_task().set_name(f"comment_processor_{queue_id}")

    try:
        processor = create_comment_processor()
        await processor.run_processor(
            queue_id=queue_id,
            name="comment",
            input_queue=queue_comment,
            output_queue=output_queue,
            http_session=http_session,
            my_logger=my_logger
        )
    except Exception as e:
        # Re-raise the exception - task state will be tracked automatically
        raise


async def consume_issue_links(
    queue_id: int,
        name: str,
    queue_issue_links: Queue,
    output_queue: Queue,  # This will be queue_upsert_others for issue_links
    project_key: str,

    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_issue_links function using the new processor architecture."""
    # Set task name for monitoring
    # asyncio.current_task().set_name(f"issue_links_processor_{queue_id}")

    try:
        processor = create_issue_links_processor()
        await processor.run_processor(
            queue_id=queue_id,
            name="issue_links",
            input_queue=queue_issue_links,
            output_queue=output_queue,
            http_session=http_session,
            my_logger=my_logger
        )
    except Exception as e:
        # Re-raise the exception - task state will be tracked automatically
        raise


async def consume_issue(
    queue_id: int,
        name: str,
    queue_issue: Queue,
    output_queue: Queue,  # This will be queue_upsert_issue for consume_issue
    project_key: str,
    # pg_async_session: AsyncSession,
    http_session: aiohttp.ClientSession,
    my_logger=None
):
    """Enhanced consume_issue function using the new processor architecture."""
    # Set task name for monitoring
    # asyncio.current_task().set_name(f"issue_processor_{queue_id}")

    if my_logger:
        my_logger.info(f"DEBUG: consume_issue function called!")
    print(f"DEBUG: consume_issue function called!")

    try:
        processor = create_issue_processor()

        # Add debugging to verify correct processor type
        if my_logger:
            my_logger.info(f"Created processor type: {type(processor).__name__}")
            if not isinstance(processor, IssueProcessor):
                my_logger.error(f"ERROR: Expected IssueProcessor, got {type(processor).__name__}")
                raise TypeError(f"Expected IssueProcessor, got {type(processor).__name__}")

        await processor.run_processor(
            queue_id=queue_id,
            name="issue",
            input_queue=queue_issue,
            output_queue=output_queue,
            http_session=http_session,
            my_logger=my_logger
        )

        # Signal that consume_issue processing is complete for early processing trigger
        try:
            from dags.data_pipeline.scalable_coordination import coordination_manager
            await coordination_manager.signal_consume_issue_complete()
            if my_logger:
                my_logger.info("Signaled consume_issue completion for immediate processing")
        except Exception as signal_error:
            if my_logger:
                my_logger.warning(f"Failed to signal consume_issue completion: {signal_error}")

    except Exception as e:
        # Re-raise the exception - task state will be tracked automatically
        raise


__all__ = [
    # Base classes
    # Specialized processor classes
    # Factory functions
    # Consumer functions
]


async def fetch_changelog(
        session: aiohttp.ClientSession, base_url: str,
        issue_key: str,
        params: dict,
        my_logger: Logger,

) -> Any:
    asyncio.current_task().set_name(f"fetch_changelog_{issue_key}")
    url = f"{base_url}/rest/api/3/issue/{issue_key}/changelog"
    try:
        return await fetch_with_retries_get(session, url, params)
    except Exception as e:
        my_logger.error(f"Exception in fetch_changelog for {issue_key}: {e}", exc_info=True)
        return {"success": False, "exception": str(e)}


async def fetch_worklog(
    session: aiohttp.ClientSession, base_url: str,
        issue_key: str,
        my_logger: Logger,
) -> Any:
    asyncio.current_task().set_name(f"fetch_worklog_{issue_key}")
    my_logger.debug(f"fetch_worklog_{issue_key}")

    url = f"{base_url}/rest/api/3/issue/{issue_key}/worklog"
    params = {
        "startAt": 0,
        "maxResults": 5000
    }
    all_worklogs = []
    try:
        while True:
            response = await fetch_with_retries_get(session, url, params)
            if not response.get("success"):
                my_logger.error(
                    f"Failed to fetch worklogs for issue {issue_key}: {response.get('exception')}")
                return {"success": False, "exception": response.get("exception")}
            result = response.get("result", {})
            worklogs = result.get("worklogs", [])
            all_worklogs.extend(worklogs)
            if len(worklogs) < params["maxResults"]:
                break
            params["startAt"] += params["maxResults"]
        return {"success": True, "result": all_worklogs}
    except Exception as e:
        my_logger.error(f"Exception in fetch_worklog for {issue_key}: {e}", exc_info=True)
        return {"success": False, "exception": str(e)}


async def fetch_comments(
        session: aiohttp.ClientSession, base_url: str, issue_key: str,
        my_logger: Logger,
) -> Dict[str, Any]:
    asyncio.current_task().set_name(f"fetch_comments_{issue_key}")

    url = f"{base_url}/rest/api/3/issue/{issue_key}/comment"
    params = {
        "startAt": 20,
        "maxResults": 100
    }
    all_comments = []
    try:
        while True:
            response = await fetch_with_retries_get(session, url, params)
            if not response.get("success"):
                my_logger.error(f"Failed to fetch comments for issue {issue_key}: {response.get('exception')}")
                return {"comments": [], "success": False}
            result = response.get("result", {})
            comments = result.get("comments", [])
            all_comments.extend(comments)
            if len(comments) < params['maxResults']:
                break
            params["startAt"] += params["maxResults"]
        return {"comments": all_comments, "success": True}
    except Exception as e:
        my_logger.error(f"Exception in fetch_comments for {issue_key}: {e}", exc_info=True)
        return {"comments": [], "success": False}
