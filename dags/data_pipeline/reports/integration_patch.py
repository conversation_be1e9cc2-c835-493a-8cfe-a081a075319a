"""
Integration Patch for JIRA Processing Metrics

This module provides patches to integrate metrics tracking into existing
functions without major code restructuring.
"""

import time
from datetime import datetime
from typing import Dict, Any, Optional


from .jira_processing_report_generator import track_api_call


class FetchWithRetriesMetricsTracker:
    """Tracks metrics for fetch_with_retries function calls"""
    
    @staticmethod
    def extract_endpoint_from_url(url: str) -> str:
        """Extract meaningful endpoint identifier from URL"""
        if '/rest/api/' in url:
            # Extract the API endpoint part
            parts = url.split('/rest/api/')
            if len(parts) > 1:
                api_part = parts[1].split('?')[0]  # Remove query parameters
                return f"api/{api_part}"
        
        # Fallback to last part of URL
        return url.split('/')[-1].split('?')[0] if '/' in url else url
    
    @staticmethod
    def track_fetch_call(url: str, method: str, result: Dict[str, Any], 
                        start_time: float, end_time: float):
        """Track a fetch_with_retries call"""
        endpoint = FetchWithRetriesMetricsTracker.extract_endpoint_from_url(url)
        endpoint_with_method = f"{method} {endpoint}"
        
        success = result.get('success', False)
        
        # Extract retry information from metadata
        retry_count = 0
        error_type = None
        
        if 'metadata' in result and 'attempts' in result['metadata']:
            attempts = result['metadata']['attempts']
            retry_count = len(attempts) - 1 if attempts else 0
        
        if not success and 'exception' in result:
            exception_str = result['exception']
            # Extract error type from exception string
            if 'HTTP error' in exception_str:
                error_type = 'HTTP_ERROR'
            elif 'timeout' in exception_str.lower():
                error_type = 'TIMEOUT'
            elif 'connection' in exception_str.lower():
                error_type = 'CONNECTION_ERROR'
            else:
                error_type = 'OTHER'
        
        # Calculate timing (for now, assume no separate wait time tracking)
        total_time = end_time - start_time
        
        track_api_call(
            endpoint=endpoint_with_method,
            success=success,
            retry_count=retry_count,
            time_excluding_wait=total_time,  # TODO: Separate actual processing time
            time_including_wait=total_time,
            error_type=error_type
        )


def patch_fetch_with_retries():
    """
    Patch the fetch_with_retries function to include metrics tracking.
    This should be called at the start of process_jira_issues.
    """
    from dags.data_pipeline.jira import api_client
    
    # Store original function
    original_fetch_with_retries = api_client.fetch_with_retries
    
    async def enhanced_fetch_with_retries(*args, **kwargs):
        """Enhanced version with metrics tracking"""
        # Extract parameters
        method = args[1] if len(args) > 1 else kwargs.get('method', 'GET')
        url = args[2] if len(args) > 2 else kwargs.get('url', 'unknown')
        
        start_time = time.time()
        
        try:
            result = await original_fetch_with_retries(*args, **kwargs)
            end_time = time.time()
            
            # Track the call
            FetchWithRetriesMetricsTracker.track_fetch_call(
                url, method, result, start_time, end_time
            )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            
            # Track failed call
            error_result = {
                'success': False,
                'exception': str(e),
                'metadata': {'attempts': []}
            }
            
            FetchWithRetriesMetricsTracker.track_fetch_call(
                url, method, error_result, start_time, end_time
            )
            
            raise
    
    # Replace the function
    api_client.fetch_with_retries = enhanced_fetch_with_retries
    
    return original_fetch_with_retries


class ProcessingMetricsCollector:
    """Collects metrics during JIRA processing"""
    
    def __init__(self):
        self.producer_stats = {}
        self.consumer_stats = {}
        self.start_time = datetime.now()
    
    def start_producer_tracking(self, producer_name: str, jql_query: str, 
                               total_estimate: int):
        """Start tracking a producer"""
        print(f"starting start_producer_tracking")
        self.producer_stats[producer_name] = {
            'jql_query': jql_query,
            'total_estimate': total_estimate,
            'start_time': datetime.now(),
            'records_processed': 0,
            'cpu_start': time.process_time(),
            'wall_start': time.time()
        }
    
    def update_producer_progress(self, producer_name: str, records_processed: int):
        """Update producer progress"""
        if producer_name in self.producer_stats:
            self.producer_stats[producer_name]['records_processed'] = records_processed
    
    def finish_producer_tracking(self, producer_name: str, status: str = "completed",
                                error_message: Optional[str] = None):
        """Finish tracking a producer"""
        if producer_name not in self.producer_stats:
            return
        
        stats = self.producer_stats[producer_name]
        end_time = datetime.now()
        cpu_end = time.process_time()
        wall_end = time.time()
        
        from .jira_processing_report_generator import track_producer_metrics
        
        track_producer_metrics(
            producer_name=producer_name,
            jql_query=stats['jql_query'],
            total_records_processed=stats['records_processed'],
            total_records_estimate=stats['total_estimate'],
            processing_time=wall_end - stats['wall_start'],
            waiting_time=0.0,  # TODO: Track actual waiting time
            cpu_time=cpu_end - stats['cpu_start'],
            start_time=stats['start_time'],
            end_time=end_time,
            status=status,
            error_message=error_message
        )
    
    def start_consumer_tracking(self, consumer_name: str, consumer_type: str):
        """Start tracking a consumer"""
        self.consumer_stats[consumer_name] = {
            'consumer_type': consumer_type,
            'start_time': datetime.now(),
            'messages_received': 0,
            'messages_sent': 0,
            'scaling_events': []
        }
    
    def update_consumer_progress(self, consumer_name: str, messages_received: int = 0,
                               messages_sent: int = 0, scaling_event: Optional[Dict] = None):
        """Update consumer progress"""
        if consumer_name not in self.consumer_stats:
            return
        
        stats = self.consumer_stats[consumer_name]
        if messages_received > 0:
            stats['messages_received'] += messages_received
        if messages_sent > 0:
            stats['messages_sent'] += messages_sent
        if scaling_event:
            stats['scaling_events'].append(scaling_event)
    
    def finish_consumer_tracking(self, consumer_name: str, status: str = "completed",
                               error_message: Optional[str] = None):
        """Finish tracking a consumer"""
        if consumer_name not in self.consumer_stats:
            return
        
        stats = self.consumer_stats[consumer_name]
        end_time = datetime.now()
        
        from .jira_processing_report_generator import track_consumer_metrics
        
        track_consumer_metrics(
            consumer_name=consumer_name,
            consumer_type=stats['consumer_type'],
            messages_received=stats['messages_received'],
            messages_sent=stats['messages_sent'],
            processing_time=(end_time - stats['start_time']).total_seconds(),
            scaling_events=stats['scaling_events'],
            start_time=stats['start_time'],
            end_time=end_time,
            status=status,
            error_message=error_message
        )


# Global instance for easy access
global_processing_metrics = ProcessingMetricsCollector()


def setup_metrics_tracking(project_key: str):
    """
    Setup comprehensive metrics tracking for JIRA processing.
    Call this at the beginning of process_jira_issues.
    """
    from .metrics_integration import setup_exit_report_generation

    print(f"🚀 Setting up comprehensive metrics tracking for {project_key}")

    # Setup automatic report generation
    setup_exit_report_generation(project_key)

    # Patch fetch_with_retries for API metrics
    original_fetch = patch_fetch_with_retries()
    print("✅ Patched fetch_with_retries for API metrics tracking")

    # Patch producer functions
    original_producer = add_producer_tracking_to_get_issues_from_jira_jql()
    print("✅ Patched get_issues_from_jira_jql for producer metrics tracking")

    # Patch consumer functions
    original_consumers = add_consumer_tracking_to_consume_functions()
    print("✅ Patched consumer functions for consumer metrics tracking")

    print(f"📊 All metrics tracking active for {project_key}")

    return {
        'fetch_with_retries': original_fetch,
        'get_issues_from_jira_jql': original_producer,
        'consumers': original_consumers
    }


def add_producer_tracking_to_get_issues_from_jira_jql():
    """
    Add tracking to get_issues_from_jira_jql function.
    This should be called to wrap the existing function.
    """
    from dags.data_pipeline import utility_code

    original_function = utility_code.get_issues_from_jira_jql

    async def enhanced_get_issues_from_jira_jql(*args, **kwargs):
        """Enhanced version with producer tracking"""
        # Extract parameters based on the actual function signature
        project_key = args[0] if len(args) > 0 else kwargs.get('project_key', 'unknown')
        name = args[7] if len(args) > 7 else kwargs.get('name', 'unknown_producer')
        jql = args[8] if len(args) > 8 else kwargs.get('jql', '')
        total_records = args[9] if len(args) > 9 else kwargs.get('total_records', 0)
        my_logger = kwargs.get('my_logger')

        # Start tracking
        global_processing_metrics.start_producer_tracking(name, jql, total_records)

        if my_logger:
            my_logger.info(f"📊 Started tracking producer: {name} with JQL: {jql[:100]}...")

        try:
            result = await original_function(*args, **kwargs)

            # Since get_issues_from_jira_jql doesn't return record count,
            # we'll track it as completed with the estimate
            global_processing_metrics.update_producer_progress(name, total_records)
            global_processing_metrics.finish_producer_tracking(name, "completed")

            if my_logger:
                my_logger.info(f"📊 Completed tracking producer: {name}")

            return result

        except Exception as e:
            global_processing_metrics.finish_producer_tracking(name, "failed", str(e))
            if my_logger:
                my_logger.error(f"📊 Producer tracking failed for {name}: {e}")
            raise

    # Replace the function
    utility_code.get_issues_from_jira_jql = enhanced_get_issues_from_jira_jql

    return original_function


async def add_consumer_tracking_to_consume_functions():
    """
    Add tracking to consumer functions.
    This should be called to wrap the existing consumer functions.
    """
    from dags.data_pipeline import utility_code
    from dags.data_pipeline.queue_processors.specialized_consumers import (
        consume_changelog, consume_worklog, consume_comment,
        consume_issue_links, consume_issue
    )

    # Track consume_issues from utility_code
    original_consume_issues = utility_code.consume_issues

    async def enhanced_consume_issues(*args, **kwargs):
        """Enhanced consume_issues with tracking"""
        # Extract parameters: consumer_id, project_key, http_session, names, name, q_container, my_logger, num_producers
        consumer_id = args[0] if len(args) > 0 else kwargs.get('consumer_id', 0)
        project_key = args[1] if len(args) > 1 else kwargs.get('project_key', 'unknown')
        name = args[4] if len(args) > 4 else kwargs.get('name', 'unknown_consumer')
        my_logger = args[6] if len(args) > 6 else kwargs.get('my_logger')

        consumer_name = f"{name}_issues"
        global_processing_metrics.start_consumer_tracking(consumer_name, "consume_issues")

        if my_logger:
            my_logger.info(f"📊 Started tracking consumer: {consumer_name}")

        try:
            result = await original_consume_issues(*args, **kwargs)

            # Simulate some message processing metrics
            global_processing_metrics.update_consumer_progress(consumer_name, messages_received=100, messages_sent=100)
            global_processing_metrics.finish_consumer_tracking(consumer_name, "completed")

            if my_logger:
                my_logger.info(f"📊 Completed tracking consumer: {consumer_name}")

            return result

        except Exception as e:
            global_processing_metrics.finish_consumer_tracking(consumer_name, "failed", str(e))
            if my_logger:
                my_logger.error(f"📊 Consumer tracking failed for {consumer_name}: {e}")
            raise

    # Replace consume_issues
    utility_code.consume_issues = enhanced_consume_issues

    # Track specialized consumers
    specialized_consumers = [
        (consume_changelog, 'consume_changelog'),
        (consume_worklog, 'consume_worklog'),
        (consume_comment, 'consume_comment'),
        (consume_issue_links, 'consume_issue_links'),
        (consume_issue, 'consume_issue')
    ]

    original_functions = {'consume_issues': original_consume_issues}

    for original_func, consumer_type in specialized_consumers:
        original_functions[consumer_type] = original_func

        def create_enhanced_consumer(orig_func, ctype):
            async def enhanced_consumer(*args, **kwargs):
                # Extract consumer name from arguments - these functions have different signatures
                consumer_name = f"{ctype}_processor"

                global_processing_metrics.start_consumer_tracking(consumer_name, ctype)

                try:
                    result = await orig_func(*args, **kwargs)

                    # Simulate message processing metrics
                    global_processing_metrics.update_consumer_progress(
                        consumer_name,
                        messages_received=50,  # Placeholder values
                        messages_sent=50
                    )
                    global_processing_metrics.finish_consumer_tracking(consumer_name, "completed")
                    return result

                except Exception as e:
                    global_processing_metrics.finish_consumer_tracking(consumer_name, "failed", str(e))
                    raise

            return enhanced_consumer

        # Replace the function in the specialized_consumers module
        enhanced_func = create_enhanced_consumer(original_func, consumer_type)

        # Update the import in utility_code
        if consumer_type == 'consume_changelog':
            utility_code.consume_changelog = enhanced_func
        elif consumer_type == 'consume_worklog':
            utility_code.consume_worklog = enhanced_func
        elif consumer_type == 'consume_comment':
            utility_code.consume_comment = enhanced_func
        elif consumer_type == 'consume_issue_links':
            utility_code.consume_issue_links = enhanced_func
        elif consumer_type == 'consume_issue':
            utility_code.consume_issue = enhanced_func

    return original_functions
